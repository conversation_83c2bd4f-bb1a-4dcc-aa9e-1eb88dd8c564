# Makefile for Minecraft-like game with raylib

# Path to raylib (provided by the user)
RAYLIB_PATH = /Users/<USER>/.local/homebrew/opt/raylib/

# Compiler
CXX = g++

# Compiler flags
CXXFLAGS = -std=c++11 -I$(RAYLIB_PATH)/include -DRLGL_SUPPORT_SKYBOX_GENERATION

# Linker flags
LDFLAGS = -L$(RAYLIB_PATH)/lib -lraylib -framework OpenGL -framework Cocoa -framework IOKit -framework CoreVideo

# Target
TARGET = game

# Source files
SRCS = main.cpp

all: $(TARGET)

$(TARGET): $(SRCS)
	$(CXX) $(CXXFLAGS) -o $@ $^ $(LDFLAGS)

clean:
	rm -f $(TARGET)

.PHONY: all clean
