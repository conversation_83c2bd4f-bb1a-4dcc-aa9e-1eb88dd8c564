#version 330

// Input vertex attributes
in vec3 vertexPosition;

// Input uniform values
uniform mat4 view;
uniform mat4 projection;

// Output vertex attributes (to fragment shader)
out vec3 fragPosition;

void main()
{
    fragPosition = vertexPosition;
    mat4 rotView = mat4(mat3(view)); // Remove translation from the view matrix
    vec4 clipPos = projection * rotView * vec4(vertexPosition, 1.0);

    gl_Position = clipPos.xyww;
}
