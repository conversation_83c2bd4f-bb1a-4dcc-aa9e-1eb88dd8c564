#include "raylib.h"
#include "raymath.h" // Required for vector operations like Vector3Add, Vector3Scale, etc.
#include <stdlib.h>  // Required for srand, rand
#include <time.h>    // Required for time

// Terrain Generation Parameters
#define MAP_SIZE_X 100        // Number of blocks in X direction
#define MAP_SIZE_Z 100        // Number of blocks in Z direction
#define TERRAIN_BLOCK_SIZE 1.0f // Size of each terrain cube
#define NOISE_SCALE 0.05f       // How "zoomed in" the noise is (smaller = larger features)
#define HEIGHT_SCALE 10.0f      // Maximum height variation of the terrain

// Function to draw a simple tree
void DrawTree(Vector3 position, float size, Color color) {
    // Draw trunk
    // Adjust trunk position so its base is at 'position'
    DrawCube((Vector3){ position.x, position.y + size / 2.0f, position.z }, size / 10.0f, size, size / 10.0f, BROWN);

    // Draw leaves
    // Adjust leaves position relative to the top of the trunk
    DrawSphere((Vector3){ position.x, position.y + size + size / 4.0f, position.z }, size / 2.0f, color);
}

// --- PLACEHOLDER GetNoise2D ---
// If your raylib version is too old and doesn't have GetNoise2D,
// you can use this placeholder. It's NOT real Perlin noise.
// If you update raylib and later get a "redefinition of GetNoise2D" error,
// please REMOVE this function.
float GetNoise2D(float x, float y) {
    // A very simple periodic function, not true Perlin noise.
    // Returns values roughly between -1 and 1.
    // For proper noise, update raylib or implement/find a Perlin noise function.
    return sinf(x * 0.3f) * cosf(y * 0.3f); // Adjust multipliers for different patterns
}
// --- END PLACEHOLDER ---

// Function to get terrain height at a specific world coordinate using Perlin-like noise
float GetTerrainHeightAt(float x, float z) {
    // GetNoise2D returns values between -1.0f and 1.0f
    float noise = GetNoise2D(x * NOISE_SCALE, z * NOISE_SCALE);
    return noise * HEIGHT_SCALE; // Scale it to our desired height range
}

int main() {
    // Initialization
    const int screenWidth = 800;
    const int screenHeight = 450;
    InitWindow(screenWidth, screenHeight, "Minecraft-like Game");

    DisableCursor(); // Hide cursor and lock it for unlimited movement
    // Set target FPS (frames per second)
    SetTargetFPS(60);
    srand(time(NULL)); // Seed random number generator for tree placement variation

    // Define the camera to look into our 3D world (position, target, up vector)
    // We'll set this up for a first-person perspective
    Camera3D camera = { 0 };
    // Initial player/camera X and Z position
    float playerInitialX = 0.0f;
    float playerInitialZ = 0.0f;
    float playerHeight = 1.8f; // Approximate eye level for camera position
    float initialTerrainY = GetTerrainHeightAt(playerInitialX, playerInitialZ);
    camera.position = (Vector3){ playerInitialX, initialTerrainY + playerHeight, playerInitialZ };
    camera.target = (Vector3){ playerInitialX, initialTerrainY + playerHeight, playerInitialZ - 1.0f }; // Look forward
    camera.up = (Vector3){ 0.0f, 1.0f, 0.0f };          // Camera up vector (rotation towards target)
    camera.fovy = 60.0f;                                // Camera field-of-view Y
    camera.projection = CAMERA_PERSPECTIVE;         // Camera projection type

    // Set camera mode to first person
    // This enables raylib's built-in handling for WASD movement and mouse look
    // With older raylib, we pass the mode directly to UpdateCamera.

    // Player physics properties
    float playerVelocityY = 0.0f;
    float gravity = 25.0f; // Gravity strength
    float jumpStrength = 10.0f; // How high the player jumps
    bool playerIsOnGround = false;

    // Main game loop
    while (!WindowShouldClose()) {
        // Update
        float deltaTime = GetFrameTime();

        // Update camera: handles mouse look and WASD movement based on CAMERA_FIRST_PERSON mode.
        // For older raylib versions, pass the mode directly.
        UpdateCamera(&camera, CAMERA_FIRST_PERSON);

        // Store camera's Y position before physics changes to help maintain look direction
        float previousCameraY = camera.position.y;

        // --- Handle Gravity and Jumping ---
        // Determine the ground level directly below the player
        float currentGroundLevel = GetTerrainHeightAt(camera.position.x, camera.position.z);


        // Check if player is on the ground
        // A small epsilon is used here to avoid floating point inaccuracies when checking ground collision
        if (camera.position.y <= currentGroundLevel + playerHeight + 0.01f) {
            // Only reset velocity and snap to ground if not moving upwards (e.g. from a jump)
            if (playerVelocityY <= 0) {
                playerVelocityY = 0;
                camera.position.y = currentGroundLevel + playerHeight; // Snap to ground precisely
            }
            playerIsOnGround = true;
        } else {
            playerIsOnGround = false;
        }

        if (playerIsOnGround && IsKeyPressed(KEY_SPACE)) { // Jump if on ground and space is pressed
            playerVelocityY = jumpStrength;
            playerIsOnGround = false; // Immediately consider player airborne after initiating jump
        }

        // Apply gravity if the player is not on the ground (or just started a jump)
        if (!playerIsOnGround) { 
            playerVelocityY -= gravity * deltaTime;
        }

        // Apply vertical velocity to camera position
        camera.position.y += playerVelocityY * deltaTime;
        // Adjust camera target's Y to maintain look direction after Y position change
        camera.target.y += (camera.position.y - previousCameraY);
        // Draw
        BeginDrawing();
        ClearBackground(SKYBLUE);
        BeginMode3D(camera);

        // --- Draw Terrain and Trees ---
        for (int i = -MAP_SIZE_X / 2; i < MAP_SIZE_X / 2; i++) {
            for (int k = -MAP_SIZE_Z / 2; k < MAP_SIZE_Z / 2; k++) {
                float worldX = i * TERRAIN_BLOCK_SIZE;
                float worldZ = k * TERRAIN_BLOCK_SIZE;
                float terrainSurfaceY = GetTerrainHeightAt(worldX, worldZ);

                // Draw terrain block (cube)
                // The center of the cube is (worldX, terrainSurfaceY - TERRAIN_BLOCK_SIZE / 2.0f, worldZ)
                // so that its top surface is at terrainSurfaceY.
                DrawCube((Vector3){worldX, terrainSurfaceY - TERRAIN_BLOCK_SIZE / 2.0f, worldZ},
                         TERRAIN_BLOCK_SIZE, TERRAIN_BLOCK_SIZE, TERRAIN_BLOCK_SIZE,
                         (Color){60, 120, 30, 255}); // A dark green for terrain

                // Randomly place trees on this terrain block
                // Use a different noise scale or simple random chance for tree placement
                // Ensure trees are not too close to the player's starting point for clarity
                if (Vector3DistanceSqr((Vector3){worldX, terrainSurfaceY, worldZ}, (Vector3){playerInitialX, initialTerrainY, playerInitialZ}) > 25.0f) { 
                    // Make tree placement and appearance deterministic for each location
                    unsigned int treeSeed = 0;
                    // Create a somewhat unique seed based on world coordinates (integer parts)
                    int iWorldX = (int)(worldX * 10.0f); 
                    int iWorldZ = (int)(worldZ * 10.0f);
                    // Simple hash-like combination for a unique seed per location
                    treeSeed = (unsigned int)(iWorldX * 73856093) ^ (unsigned int)(iWorldZ * 19349663) ^ (unsigned int)(iWorldX + iWorldZ * 83473);
                    
                    if (GetNoise2D(worldX * 0.1f, worldZ * 0.1f) > 0.55f && (treeSeed % 20) == 0) { // Deterministic placement
                        srand(treeSeed); // Seed random for this specific tree's properties
                        DrawTree((Vector3){worldX, terrainSurfaceY, worldZ}, 2.0f + (GetRandomValue(-5, 5) / 10.0f), (Color){0, 80 + GetRandomValue(0,20), 0, 255});
                    }
                }
            }
        }

        EndMode3D();

        // Draw FPS counter
        DrawFPS(10, 10);

        EndDrawing();

    }

    // De-Initialization
    EnableCursor(); // Re-enable cursor before closing
    CloseWindow();

    return 0;
}
