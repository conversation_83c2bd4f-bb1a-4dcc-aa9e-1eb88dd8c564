#include "raylib.h"
#include "rlgl.h"
#include "raymath.h"
#define DB_PERLIN_IMPL
#include "db_perlin.hpp"
#include "rlgl.h"

#include <vector>
#include <cstring>
#include <cmath>

// Terrain size
const int TERRAIN_GRID_WIDTH = 10;
const int TERRAIN_GRID_DEPTH = 10;
const int TERRAIN_CHUNK_SIZE = 32;
const int NUM_LODS = 3;

struct TerrainChunk {
    Model lods[NUM_LODS];
    Vector3 position;
};

Mesh GenTerrainMesh(int chunkSize, int lod, Vector3 chunkPosition) {
    std::vector<float> vertices;
    std::vector<unsigned short> indices;
    std::vector<float> texcoords;
    std::vector<float> normals;

    int step = 1 << lod;
    int lodChunkSize = chunkSize / step;

    // Generate vertices and texcoords
    for (int z = 0; z <= lodChunkSize; z++) {
        for (int x = 0; x <= lodChunkSize; x++) {
            float localX = x * step - chunkSize/2.0f;
            float localZ = z * step - chunkSize/2.0f;

            float noiseX = chunkPosition.x + localX;
            float noiseZ = chunkPosition.z + localZ;

            float y = db::perlin<float>(noiseX * 0.1f, noiseZ * 0.1f) * 10.0f;

            vertices.push_back(localX);
            vertices.push_back(y);
            vertices.push_back(localZ);
            texcoords.push_back((float)(x * step) / chunkSize);
            texcoords.push_back((float)(z * step) / chunkSize);
        }
    }

    // Generate indices
    for (int z = 0; z < lodChunkSize; z++) {
        for (int x = 0; x < lodChunkSize; x++) {
            unsigned short topLeft = z * (lodChunkSize + 1) + x;
            unsigned short topRight = topLeft + 1;
            unsigned short bottomLeft = (z + 1) * (lodChunkSize + 1) + x;
            unsigned short bottomRight = bottomLeft + 1;

            indices.push_back(topLeft);
            indices.push_back(bottomLeft);
            indices.push_back(topRight);

            indices.push_back(topRight);
            indices.push_back(bottomLeft);
            indices.push_back(bottomRight);
        }
    }

    // Add a skirt to hide seams
    int baseVertexIndex = vertices.size() / 3;
    float skirtDepth = 20.0f; // How far down the skirt goes

    // Bottom edge
    for (int x = 0; x <= lodChunkSize; x++) {
        int topIndex = x;
        Vector3 topVertex = { vertices[topIndex*3], vertices[topIndex*3+1], vertices[topIndex*3+2] };
        vertices.push_back(topVertex.x); vertices.push_back(topVertex.y - skirtDepth); vertices.push_back(topVertex.z);
        texcoords.push_back((float)(x * step) / chunkSize); texcoords.push_back(1.0f);
    }
    for (int x = 0; x < lodChunkSize; x++) {
        unsigned short tl = x;
        unsigned short tr = x + 1;
        unsigned short bl = baseVertexIndex + x;
        unsigned short br = baseVertexIndex + x + 1;
        indices.push_back(tl); indices.push_back(bl); indices.push_back(tr);
        indices.push_back(tr); indices.push_back(bl); indices.push_back(br);
    }
    baseVertexIndex += (lodChunkSize + 1);

    // Top edge
    for (int x = 0; x <= lodChunkSize; x++) {
        int topIndex = lodChunkSize * (lodChunkSize + 1) + x;
        Vector3 topVertex = { vertices[topIndex*3], vertices[topIndex*3+1], vertices[topIndex*3+2] };
        vertices.push_back(topVertex.x); vertices.push_back(topVertex.y - skirtDepth); vertices.push_back(topVertex.z);
        texcoords.push_back((float)(x * step) / chunkSize); texcoords.push_back(0.0f);
    }
    for (int x = 0; x < lodChunkSize; x++) {
        unsigned short tl = lodChunkSize * (lodChunkSize + 1) + x;
        unsigned short tr = tl + 1;
        unsigned short bl = baseVertexIndex + x;
        unsigned short br = baseVertexIndex + x + 1;
        indices.push_back(tr); indices.push_back(br); indices.push_back(tl);
        indices.push_back(tl); indices.push_back(br); indices.push_back(bl);
    }
    baseVertexIndex += (lodChunkSize + 1);

    // Left edge
    for (int z = 0; z <= lodChunkSize; z++) {
        int topIndex = z * (lodChunkSize + 1);
        Vector3 topVertex = { vertices[topIndex*3], vertices[topIndex*3+1], vertices[topIndex*3+2] };
        vertices.push_back(topVertex.x); vertices.push_back(topVertex.y - skirtDepth); vertices.push_back(topVertex.z);
        texcoords.push_back(0.0f); texcoords.push_back((float)(z * step) / chunkSize);
    }
    for (int z = 0; z < lodChunkSize; z++) {
        unsigned short tl = z * (lodChunkSize + 1);
        unsigned short bl = (z + 1) * (lodChunkSize + 1);
        unsigned short tr = baseVertexIndex + z;
        unsigned short br = baseVertexIndex + z + 1;
        indices.push_back(tr); indices.push_back(br); indices.push_back(tl);
        indices.push_back(tl); indices.push_back(br); indices.push_back(bl);
    }
    baseVertexIndex += (lodChunkSize + 1);

    // Right edge
    for (int z = 0; z <= lodChunkSize; z++) {
        int topIndex = z * (lodChunkSize + 1) + lodChunkSize;
        Vector3 topVertex = { vertices[topIndex*3], vertices[topIndex*3+1], vertices[topIndex*3+2] };
        vertices.push_back(topVertex.x); vertices.push_back(topVertex.y - skirtDepth); vertices.push_back(topVertex.z);
        texcoords.push_back(1.0f); texcoords.push_back((float)(z * step) / chunkSize);
    }
    for (int z = 0; z < lodChunkSize; z++) {
        unsigned short tl = z * (lodChunkSize + 1) + lodChunkSize;
        unsigned short bl = (z + 1) * (lodChunkSize + 1) + lodChunkSize;
        unsigned short tr = baseVertexIndex + z;
        unsigned short br = baseVertexIndex + z + 1;
        indices.push_back(tl); indices.push_back(bl); indices.push_back(tr);
        indices.push_back(tr); indices.push_back(bl); indices.push_back(br);
    }

    Mesh mesh = { 0 };
    mesh.vertexCount = vertices.size() / 3;
    mesh.triangleCount = indices.size() / 3;

    mesh.vertices = (float *)MemAlloc(vertices.size() * sizeof(float));
    memcpy(mesh.vertices, vertices.data(), vertices.size() * sizeof(float));

    mesh.texcoords = (float *)MemAlloc(texcoords.size() * sizeof(float));
    memcpy(mesh.texcoords, texcoords.data(), texcoords.size() * sizeof(float));

    mesh.indices = (unsigned short *)MemAlloc(indices.size() * sizeof(unsigned short));
    memcpy(mesh.indices, indices.data(), indices.size() * sizeof(unsigned short));

    // Calculate normals
    normals.resize(vertices.size(), 0.0f);
    for (size_t i = 0; i < indices.size(); i += 3) {
        unsigned short i1 = indices[i];
        unsigned short i2 = indices[i+1];
        unsigned short i3 = indices[i+2];

        Vector3 v1 = { vertices[i1*3], vertices[i1*3+1], vertices[i1*3+2] };
        Vector3 v2 = { vertices[i2*3], vertices[i2*3+1], vertices[i2*3+2] };
        Vector3 v3 = { vertices[i3*3], vertices[i3*3+1], vertices[i3*3+2] };

        Vector3 edge1 = { v2.x - v1.x, v2.y - v1.y, v2.z - v1.z };
        Vector3 edge2 = { v3.x - v1.x, v3.y - v1.y, v3.z - v1.z };

        Vector3 normal = Vector3CrossProduct(edge1, edge2);
        normal = Vector3Normalize(normal);

        normals[i1*3] += normal.x; normals[i1*3+1] += normal.y; normals[i1*3+2] += normal.z;
        normals[i2*3] += normal.x; normals[i2*3+1] += normal.y; normals[i2*3+2] += normal.z;
        normals[i3*3] += normal.x; normals[i3*3+1] += normal.y; normals[i3*3+2] += normal.z;
    }

    // Normalize all normals
    for (size_t i = 0; i < normals.size(); i+=3) {
        Vector3 normal = { normals[i], normals[i+1], normals[i+2] };
        normal = Vector3Normalize(normal);
        normals[i] = normal.x;
        normals[i+1] = normal.y;
        normals[i+2] = normal.z;
    }

    mesh.normals = (float *)MemAlloc(normals.size() * sizeof(float));
    memcpy(mesh.normals, normals.data(), normals.size() * sizeof(float));

    UploadMesh(&mesh, true);

    return mesh;
}

int main() {
    const int screenWidth = 800;
    const int screenHeight = 450;

    InitWindow(screenWidth, screenHeight, "Smooth Terrain Example");

    Camera3D camera = { 0 };
    camera.position = (Vector3){ 50.0f, 50.0f, 50.0f };
    camera.target = (Vector3){ 0.0f, 0.0f, 0.0f };
    camera.up = (Vector3){ 0.0f, 1.0f, 0.0f };
    camera.fovy = 45.0f;
    camera.projection = CAMERA_PERSPECTIVE;

    Shader shader = LoadShader("lighting.vs", "lighting.fs");
    Image checked = GenImageChecked(2, 2, 1, 1, DARKGRAY, WHITE);
    Texture2D texture = LoadTextureFromImage(checked);
    UnloadImage(checked);

    std::vector<TerrainChunk> terrain(TERRAIN_GRID_WIDTH * TERRAIN_GRID_DEPTH);

    for (int z = 0; z < TERRAIN_GRID_DEPTH; z++) {
        for (int x = 0; x < TERRAIN_GRID_WIDTH; x++) {
            int index = z * TERRAIN_GRID_WIDTH + x;
            terrain[index].position = {
                (x - TERRAIN_GRID_WIDTH / 2.0f) * TERRAIN_CHUNK_SIZE,
                0,
                (z - TERRAIN_GRID_DEPTH / 2.0f) * TERRAIN_CHUNK_SIZE
            };

            for (int lod = 0; lod < NUM_LODS; lod++) {
                Mesh mesh = GenTerrainMesh(TERRAIN_CHUNK_SIZE, lod, terrain[index].position);
                terrain[index].lods[lod] = LoadModelFromMesh(mesh);
                terrain[index].lods[lod].materials[0].shader = shader;
                terrain[index].lods[lod].materials[0].maps[MATERIAL_MAP_DIFFUSE].texture = texture;
            }
        }
    }

    Vector3 lightDir = Vector3Normalize((Vector3){ -1.0f, -1.0f, -1.0f });
    float lightColor[] = { 1.0f, 1.0f, 1.0f, 1.0f };

    int lightDirLoc = GetShaderLocation(shader, "lightDir");
    int lightColorLoc = GetShaderLocation(shader, "lightColor");
    int viewPosLoc = GetShaderLocation(shader, "viewPos");

    SetShaderValue(shader, lightDirLoc, &lightDir, SHADER_UNIFORM_VEC3);
    SetShaderValue(shader, lightColorLoc, lightColor, SHADER_UNIFORM_VEC4);

    // Load skybox (PNG fallback)
    Shader shdrSkybox = LoadShader("skybox.vs", "skybox.fs");
    Model skybox = { 0 };
    bool skyboxLoaded = false;

    Image panoramaImg = LoadImage("skybox.png");
    if (panoramaImg.data != NULL) {
        skyboxLoaded = true;
        TextureCubemap cubemap = LoadTextureCubemap(panoramaImg, CUBEMAP_LAYOUT_AUTO_DETECT);
        UnloadImage(panoramaImg);

        skybox = LoadModelFromMesh(GenMeshCube(50.0f, 50.0f, 50.0f));
        skybox.materials[0].shader = shdrSkybox;
        // Bind cubemap texture to samplerCube uniform explicitly
        int cubeLoc = GetShaderLocation(shdrSkybox, "skybox");
        SetShaderValueTexture(shdrSkybox, cubeLoc, cubemap);
        skybox.materials[0].maps[MATERIAL_MAP_CUBEMAP].texture = cubemap;
    } else {
        TraceLog(LOG_WARNING, "Failed to load skybox texture: skybox.png");
    }

    SetTargetFPS(60);

    while (!WindowShouldClose()) {
        UpdateCamera(&camera, CAMERA_FIRST_PERSON);

        float cameraPos[] = { camera.position.x, camera.position.y, camera.position.z };
        SetShaderValue(shader, viewPosLoc, cameraPos, SHADER_UNIFORM_VEC3);

        BeginDrawing();
            ClearBackground(RAYWHITE);
            // Update skybox shader matrices
            if (skyboxLoaded) {
                Matrix camView = GetCameraMatrix(camera); // view matrix
                camView.m12 = camView.m13 = camView.m14 = 0.0f; // remove translation
                Matrix camProj = MatrixPerspective(camera.fovy*DEG2RAD, (float)GetScreenWidth()/GetScreenHeight(), 0.01f, 1000.0f);
                SetShaderValueMatrix(shdrSkybox, GetShaderLocation(shdrSkybox, "view"), camView);
                SetShaderValueMatrix(shdrSkybox, GetShaderLocation(shdrSkybox, "projection"), camProj);
            }
            BeginMode3D(camera);
                if (skyboxLoaded) {
                    rlDisableBackfaceCulling();
                    rlDisableDepthMask();
                    DrawModel(skybox, camera.position, 1.0f, WHITE);
                    rlEnableDepthMask();
                    rlEnableBackfaceCulling();
                }
                for (const auto& chunk : terrain) {
                    float distance = Vector3Distance(camera.position, chunk.position);
                    int lod = 0;
                    if (distance > 200) lod = 2;
                    else if (distance > 100) lod = 1;
                    DrawModel(chunk.lods[lod], chunk.position, 1.0f, WHITE);
                }
            EndMode3D();
            DrawFPS(10, 10);
        EndDrawing();
    }

    UnloadShader(shader);
    UnloadTexture(texture);

    if (skyboxLoaded) {
        UnloadTexture(skybox.materials[0].maps[MATERIAL_MAP_CUBEMAP].texture);
        UnloadModel(skybox);
    }
    UnloadShader(shdrSkybox);
    for (const auto& chunk : terrain) {
        for (int lod = 0; lod < NUM_LODS; lod++) {
            UnloadModel(chunk.lods[lod]);
        }
    }
    CloseWindow();

    return 0;
}

#if 0


    

    // STEP 1: Setup framebuffer
    //------------------------------------------------------------------------------------------
    unsigned int rbo = rlLoadTextureDepth(size, size, true);
    cubemap.id = rlLoadTextureCubemap(0, size, format, 1);

    unsigned int fbo = rlLoadFramebuffer();
    rlFramebufferAttach(fbo, rbo, RL_ATTACHMENT_DEPTH, RL_ATTACHMENT_RENDERBUFFER, 0);
    rlFramebufferAttach(fbo, cubemap.id, RL_ATTACHMENT_COLOR_CHANNEL0, RL_ATTACHMENT_CUBEMAP_POSITIVE_X, 0);

    // Check if framebuffer is complete with attachments (valid)
    if (rlFramebufferComplete(fbo)) TraceLog(LOG_INFO, "FBO: [ID %i] Framebuffer object created successfully", fbo);
    //------------------------------------------------------------------------------------------

    // STEP 2: Draw to framebuffer
    //------------------------------------------------------------------------------------------
    // NOTE: Shader is used to convert HDR equirectangular environment map to cubemap equivalent (6 faces)
    rlEnableShader(shader.id);

    // Define projection matrix and send it to shader
    Matrix matFboProjection = MatrixPerspective(90.0*DEG2RAD, 1.0, rlGetCullDistanceNear(), rlGetCullDistanceFar());
    rlSetUniformMatrix(shader.locs[SHADER_LOC_MATRIX_PROJECTION], matFboProjection);

    // Define view matrix for every side of the cubemap
    Matrix fboViews[6] = {
        MatrixLookAt((Vector3){ 0.0f, 0.0f, 0.0f }, (Vector3){  1.0f,  0.0f,  0.0f }, (Vector3){ 0.0f, -1.0f,  0.0f }),
        MatrixLookAt((Vector3){ 0.0f, 0.0f, 0.0f }, (Vector3){ -1.0f,  0.0f,  0.0f }, (Vector3){ 0.0f, -1.0f,  0.0f }),
        MatrixLookAt((Vector3){ 0.0f, 0.0f, 0.0f }, (Vector3){  0.0f,  1.0f,  0.0f }, (Vector3){ 0.0f,  0.0f,  1.0f }),
        MatrixLookAt((Vector3){ 0.0f, 0.0f, 0.0f }, (Vector3){  0.0f, -1.0f,  0.0f }, (Vector3){ 0.0f,  0.0f, -1.0f }),
        MatrixLookAt((Vector3){ 0.0f, 0.0f, 0.0f }, (Vector3){  0.0f,  0.0f,  1.0f }, (Vector3){ 0.0f, -1.0f,  0.0f }),
        MatrixLookAt((Vector3){ 0.0f, 0.0f, 0.0f }, (Vector3){  0.0f,  0.0f, -1.0f }, (Vector3){ 0.0f, -1.0f,  0.0f })
    };

    rlViewport(0, 0, size, size);   // Set viewport to current fbo dimensions

    // Activate and enable texture for drawing to cubemap faces
    rlActiveTextureSlot(0);
    rlEnableTexture(panorama.id);

    for (int i = 0; i < 6; i++)
    {
        // Set the view matrix for the current cube face
        rlSetUniformMatrix(shader.locs[SHADER_LOC_MATRIX_VIEW], fboViews[i]);

        // Select the current cubemap face attachment for the fbo
        rlFramebufferAttach(fbo, cubemap.id, RL_ATTACHMENT_COLOR_CHANNEL0, RL_ATTACHMENT_CUBEMAP_POSITIVE_X + i, 0);
        rlEnableFramebuffer(fbo);

        // Load and draw a cube, it uses the current enabled texture
        rlClearScreenBuffers();
        rlLoadDrawCube();
    }
    //------------------------------------------------------------------------------------------

    // STEP 3: Unload framebuffer and reset state
    //------------------------------------------------------------------------------------------
    rlDisableShader();          // Unbind shader
    rlDisableTexture();         // Unbind texture
    rlDisableFramebuffer();     // Unbind framebuffer
    rlUnloadFramebuffer(fbo);   // Unload framebuffer (and automatically attached depth texture/renderbuffer)

    // Reset viewport dimensions to default
    rlViewport(0, 0, rlGetFramebufferWidth(), rlGetFramebufferHeight());
    rlEnableBackfaceCulling();
    //------------------------------------------------------------------------------------------

    cubemap.width = size;
    cubemap.height = size;
    cubemap.mipmaps = 1;
    cubemap.format = format;
#endif // obsolete HDR cubemap code

