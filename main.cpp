#include "raylib.h"
#include "rlgl.h"
#include "raymath.h"
#define DB_PERLIN_IMPL
#include "db_perlin.hpp"

#include <vector>
#include <cstring>
#include <cmath>
#include <algorithm>

// Terrain size
const int TERRAIN_GRID_WIDTH = 10;
const int TERRAIN_GRID_DEPTH = 10;
const int TERRAIN_CHUNK_SIZE = 32;
const int NUM_LODS = 3;

// Sky and atmosphere constants
const float DAY_CYCLE_DURATION = 120.0f; // 2 minutes for full day cycle
const int MAX_CLOUDS = 50;
const int MAX_STARS = 200;

// Sky colors for different times of day
struct SkyColors {
    Color horizon;
    Color zenith;
    Color fog;
};

// Cloud structure
struct Cloud {
    Vector3 position;
    float size;
    float opacity;
    Vector2 velocity;
    bool active;
};

// Star structure
struct Star {
    Vector3 position;
    float brightness;
    float twinkle;
};

struct TerrainChunk {
    Model lods[NUM_LODS];
    Vector3 position;
};

// Sky system functions
SkyColors GetSkyColors(float timeOfDay) {
    SkyColors colors;

    // Normalize time to 0-1 range
    float t = fmod(timeOfDay, 1.0f);

    if (t < 0.2f) { // Night (0.0 - 0.2)
        float nightFactor = t / 0.2f;
        colors.horizon = (Color){25, 25, 112, 255}; // Dark blue
        colors.zenith = (Color){5, 5, 30, 255};     // Very dark blue
        colors.fog = (Color){10, 10, 40, 100};
    } else if (t < 0.3f) { // Dawn (0.2 - 0.3)
        float dawnFactor = (t - 0.2f) / 0.1f;
        colors.horizon = (Color){
            (unsigned char)(25 + dawnFactor * 230),   // 25 -> 255
            (unsigned char)(25 + dawnFactor * 115),   // 25 -> 140
            (unsigned char)(112 - dawnFactor * 112),  // 112 -> 0
            255
        };
        colors.zenith = (Color){
            (unsigned char)(5 + dawnFactor * 130),    // 5 -> 135
            (unsigned char)(5 + dawnFactor * 201),    // 5 -> 206
            (unsigned char)(30 + dawnFactor * 225),   // 30 -> 255
            255
        };
        colors.fog = (Color){100, 80, 60, 120};
    } else if (t < 0.7f) { // Day (0.3 - 0.7)
        colors.horizon = (Color){135, 206, 235, 255}; // Sky blue
        colors.zenith = (Color){70, 130, 180, 255};    // Steel blue
        colors.fog = (Color){200, 220, 255, 80};
    } else if (t < 0.8f) { // Dusk (0.7 - 0.8)
        float duskFactor = (t - 0.7f) / 0.1f;
        colors.horizon = (Color){
            (unsigned char)(255 - duskFactor * 30),   // 255 -> 225
            (unsigned char)(140 - duskFactor * 40),   // 140 -> 100
            (unsigned char)(duskFactor * 50),         // 0 -> 50
            255
        };
        colors.zenith = (Color){
            (unsigned char)(70 - duskFactor * 45),    // 70 -> 25
            (unsigned char)(130 - duskFactor * 105),  // 130 -> 25
            (unsigned char)(180 - duskFactor * 68),   // 180 -> 112
            255
        };
        colors.fog = (Color){120, 80, 100, 100};
    } else { // Night (0.8 - 1.0)
        float nightFactor = (t - 0.8f) / 0.2f;
        colors.horizon = (Color){
            (unsigned char)(225 - nightFactor * 200), // 225 -> 25
            (unsigned char)(100 - nightFactor * 75),  // 100 -> 25
            (unsigned char)(50 + nightFactor * 62),   // 50 -> 112
            255
        };
        colors.zenith = (Color){
            (unsigned char)(25 - nightFactor * 20),   // 25 -> 5
            (unsigned char)(25 - nightFactor * 20),   // 25 -> 5
            (unsigned char)(112 - nightFactor * 82),  // 112 -> 30
            255
        };
        colors.fog = (Color){40, 30, 60, 100};
    }

    return colors;
}

Vector3 GetSunPosition(float timeOfDay) {
    float angle = timeOfDay * 2.0f * PI - PI/2.0f; // Sun rises at 0.25, sets at 0.75
    return (Vector3){
        cosf(angle) * 1000.0f,
        sinf(angle) * 1000.0f,
        0.0f
    };
}

Vector3 GetMoonPosition(float timeOfDay) {
    float angle = (timeOfDay + 0.5f) * 2.0f * PI - PI/2.0f; // Opposite to sun
    return (Vector3){
        cosf(angle) * 1000.0f,
        sinf(angle) * 1000.0f,
        0.0f
    };
}

void InitializeClouds(Cloud* clouds) {
    for (int i = 0; i < MAX_CLOUDS; i++) {
        clouds[i].position = (Vector3){
            (float)(rand() % 2000 - 1000), // X: -1000 to 1000
            (float)(rand() % 200 + 100),   // Y: 100 to 300
            (float)(rand() % 2000 - 1000)  // Z: -1000 to 1000
        };
        clouds[i].size = (float)(rand() % 50 + 20);  // Size: 20 to 70
        clouds[i].opacity = (float)(rand() % 60 + 40) / 100.0f; // 0.4 to 1.0
        clouds[i].velocity = (Vector2){
            (float)(rand() % 20 - 10) / 100.0f, // -0.1 to 0.1
            (float)(rand() % 20 - 10) / 100.0f
        };
        clouds[i].active = true;
    }
}

void InitializeStars(Star* stars) {
    for (int i = 0; i < MAX_STARS; i++) {
        // Generate random points on a sphere
        float theta = (float)(rand()) / RAND_MAX * 2.0f * PI;
        float phi = acosf(1.0f - 2.0f * (float)(rand()) / RAND_MAX);

        float radius = 800.0f;
        stars[i].position = (Vector3){
            radius * sinf(phi) * cosf(theta),
            radius * cosf(phi),
            radius * sinf(phi) * sinf(theta)
        };
        stars[i].brightness = (float)(rand() % 100) / 100.0f;
        stars[i].twinkle = (float)(rand() % 100) / 100.0f;
    }
}

void UpdateClouds(Cloud* clouds, float deltaTime) {
    for (int i = 0; i < MAX_CLOUDS; i++) {
        if (clouds[i].active) {
            clouds[i].position.x += clouds[i].velocity.x * deltaTime * 10.0f;
            clouds[i].position.z += clouds[i].velocity.y * deltaTime * 10.0f;

            // Wrap around world
            if (clouds[i].position.x > 1000) clouds[i].position.x = -1000;
            if (clouds[i].position.x < -1000) clouds[i].position.x = 1000;
            if (clouds[i].position.z > 1000) clouds[i].position.z = -1000;
            if (clouds[i].position.z < -1000) clouds[i].position.z = 1000;
        }
    }
}

void DrawSkyGradient(Camera3D camera, SkyColors colors) {
    // Draw sky as a large sphere with gradient
    rlPushMatrix();
    rlTranslatef(camera.position.x, camera.position.y, camera.position.z);

    // Draw gradient sky dome
    rlBegin(RL_TRIANGLES);

    int segments = 32;
    float radius = 500.0f;

    for (int i = 0; i < segments; i++) {
        for (int j = 0; j < segments/2; j++) {
            float theta1 = (float)i / segments * 2.0f * PI;
            float theta2 = (float)(i + 1) / segments * 2.0f * PI;
            float phi1 = (float)j / (segments/2) * PI;
            float phi2 = (float)(j + 1) / (segments/2) * PI;

            // Calculate vertices
            Vector3 v1 = {radius * sinf(phi1) * cosf(theta1), radius * cosf(phi1), radius * sinf(phi1) * sinf(theta1)};
            Vector3 v2 = {radius * sinf(phi1) * cosf(theta2), radius * cosf(phi1), radius * sinf(phi1) * sinf(theta2)};
            Vector3 v3 = {radius * sinf(phi2) * cosf(theta1), radius * cosf(phi2), radius * sinf(phi2) * sinf(theta1)};
            Vector3 v4 = {radius * sinf(phi2) * cosf(theta2), radius * cosf(phi2), radius * sinf(phi2) * sinf(theta2)};

            // Interpolate colors based on height
            float t1 = (v1.y + radius) / (2.0f * radius);
            float t2 = (v2.y + radius) / (2.0f * radius);
            float t3 = (v3.y + radius) / (2.0f * radius);
            float t4 = (v4.y + radius) / (2.0f * radius);

            Color c1 = {
                (unsigned char)(colors.horizon.r * (1-t1) + colors.zenith.r * t1),
                (unsigned char)(colors.horizon.g * (1-t1) + colors.zenith.g * t1),
                (unsigned char)(colors.horizon.b * (1-t1) + colors.zenith.b * t1),
                255
            };
            Color c2 = {
                (unsigned char)(colors.horizon.r * (1-t2) + colors.zenith.r * t2),
                (unsigned char)(colors.horizon.g * (1-t2) + colors.zenith.g * t2),
                (unsigned char)(colors.horizon.b * (1-t2) + colors.zenith.b * t2),
                255
            };
            Color c3 = {
                (unsigned char)(colors.horizon.r * (1-t3) + colors.zenith.r * t3),
                (unsigned char)(colors.horizon.g * (1-t3) + colors.zenith.g * t3),
                (unsigned char)(colors.horizon.b * (1-t3) + colors.zenith.b * t3),
                255
            };
            Color c4 = {
                (unsigned char)(colors.horizon.r * (1-t4) + colors.zenith.r * t4),
                (unsigned char)(colors.horizon.g * (1-t4) + colors.zenith.g * t4),
                (unsigned char)(colors.horizon.b * (1-t4) + colors.zenith.b * t4),
                255
            };

            // Draw triangles
            rlColor4ub(c1.r, c1.g, c1.b, c1.a); rlVertex3f(v1.x, v1.y, v1.z);
            rlColor4ub(c3.r, c3.g, c3.b, c3.a); rlVertex3f(v3.x, v3.y, v3.z);
            rlColor4ub(c2.r, c2.g, c2.b, c2.a); rlVertex3f(v2.x, v2.y, v2.z);

            rlColor4ub(c2.r, c2.g, c2.b, c2.a); rlVertex3f(v2.x, v2.y, v2.z);
            rlColor4ub(c3.r, c3.g, c3.b, c3.a); rlVertex3f(v3.x, v3.y, v3.z);
            rlColor4ub(c4.r, c4.g, c4.b, c4.a); rlVertex3f(v4.x, v4.y, v4.z);
        }
    }

    rlEnd();
    rlPopMatrix();
}

void DrawSun(Vector3 position, float timeOfDay) {
    if (position.y > 0) { // Only draw if above horizon
        float intensity = std::max(0.0f, position.y / 1000.0f);
        Color sunColor = {
            (unsigned char)(255 * intensity),
            (unsigned char)(255 * intensity * 0.9f),
            (unsigned char)(100 * intensity),
            255
        };

        // Draw sun glow
        DrawSphere(position, 30.0f, sunColor);

        // Draw sun rays
        rlPushMatrix();
        rlTranslatef(position.x, position.y, position.z);
        rlBegin(RL_LINES);
        rlColor4ub(sunColor.r, sunColor.g, sunColor.b, 100);

        for (int i = 0; i < 8; i++) {
            float angle = i * PI / 4.0f;
            float rayLength = 50.0f;
            rlVertex3f(0, 0, 0);
            rlVertex3f(cosf(angle) * rayLength, sinf(angle) * rayLength, 0);
        }
        rlEnd();
        rlPopMatrix();
    }
}

void DrawMoon(Vector3 position, float timeOfDay) {
    if (position.y > 0) { // Only draw if above horizon
        float intensity = std::max(0.0f, position.y / 1000.0f);
        Color moonColor = {
            (unsigned char)(220 * intensity),
            (unsigned char)(220 * intensity),
            (unsigned char)(255 * intensity),
            255
        };

        DrawSphere(position, 25.0f, moonColor);

        // Draw moon glow
        Color glowColor = moonColor;
        glowColor.a = 50;
        DrawSphere(position, 35.0f, glowColor);
    }
}

void DrawStars(Star* stars, float timeOfDay, float gameTime) {
    // Only draw stars at night
    float t = fmod(timeOfDay, 1.0f);
    if (t > 0.3f && t < 0.7f) return; // Don't draw during day

    float starVisibility = 1.0f;
    if (t < 0.3f) starVisibility = (0.3f - t) / 0.3f;
    else if (t > 0.7f) starVisibility = (t - 0.7f) / 0.3f;

    rlBegin(RL_TRIANGLES);
    for (int i = 0; i < MAX_STARS; i++) {
        float twinkle = sinf(gameTime * 2.0f + stars[i].twinkle * 10.0f) * 0.3f + 0.7f;
        float alpha = stars[i].brightness * starVisibility * twinkle;

        rlColor4f(1.0f, 1.0f, 1.0f, alpha);

        // Draw star as small quad
        float starSize = 0.5f;
        Vector3 pos = stars[i].position;

        // Simple billboard quad for star
        rlVertex3f(pos.x - starSize, pos.y - starSize, pos.z);
        rlVertex3f(pos.x + starSize, pos.y - starSize, pos.z);
        rlVertex3f(pos.x, pos.y + starSize, pos.z);

        rlVertex3f(pos.x - starSize, pos.y + starSize, pos.z);
        rlVertex3f(pos.x + starSize, pos.y + starSize, pos.z);
        rlVertex3f(pos.x, pos.y - starSize, pos.z);
    }
    rlEnd();
}

void DrawClouds(Cloud* clouds, Camera3D camera, float timeOfDay) {
    SkyColors skyColors = GetSkyColors(timeOfDay);

    for (int i = 0; i < MAX_CLOUDS; i++) {
        if (clouds[i].active) {
            float distance = Vector3Distance(camera.position, clouds[i].position);
            if (distance < 800.0f) { // Only draw nearby clouds

                // Calculate cloud color based on time of day
                Color cloudColor = {
                    (unsigned char)(255 * 0.9f),
                    (unsigned char)(255 * 0.9f),
                    (unsigned char)(255 * 0.9f),
                    (unsigned char)(clouds[i].opacity * 255 * 0.7f)
                };

                // Tint clouds based on sky color
                float t = fmod(timeOfDay, 1.0f);
                if (t < 0.3f || t > 0.7f) {
                    // Night/dawn/dusk - tint clouds
                    cloudColor.r = (unsigned char)(cloudColor.r * 0.6f);
                    cloudColor.g = (unsigned char)(cloudColor.g * 0.6f);
                    cloudColor.b = (unsigned char)(cloudColor.b * 0.8f);
                }

                // Draw cloud as multiple spheres for fluffy appearance
                for (int j = 0; j < 5; j++) {
                    Vector3 offset = {
                        (float)(rand() % 20 - 10),
                        (float)(rand() % 10 - 5),
                        (float)(rand() % 20 - 10)
                    };
                    Vector3 cloudPos = Vector3Add(clouds[i].position, offset);
                    float cloudSize = clouds[i].size * (0.7f + (float)(rand() % 60) / 100.0f);

                    DrawSphere(cloudPos, cloudSize, cloudColor);
                }
            }
        }
    }
}

Mesh GenTerrainMesh(int chunkSize, int lod, Vector3 chunkPosition) {
    std::vector<float> vertices;
    std::vector<unsigned short> indices;
    std::vector<float> texcoords;
    std::vector<float> normals;

    int step = 1 << lod;
    int lodChunkSize = chunkSize / step;

    // Generate vertices and texcoords
    for (int z = 0; z <= lodChunkSize; z++) {
        for (int x = 0; x <= lodChunkSize; x++) {
            float localX = x * step - chunkSize/2.0f;
            float localZ = z * step - chunkSize/2.0f;

            float noiseX = chunkPosition.x + localX;
            float noiseZ = chunkPosition.z + localZ;

            float y = db::perlin<float>(noiseX * 0.1f, noiseZ * 0.1f) * 10.0f;

            vertices.push_back(localX);
            vertices.push_back(y);
            vertices.push_back(localZ);
            texcoords.push_back((float)(x * step) / chunkSize);
            texcoords.push_back((float)(z * step) / chunkSize);
        }
    }

    // Generate indices
    for (int z = 0; z < lodChunkSize; z++) {
        for (int x = 0; x < lodChunkSize; x++) {
            unsigned short topLeft = z * (lodChunkSize + 1) + x;
            unsigned short topRight = topLeft + 1;
            unsigned short bottomLeft = (z + 1) * (lodChunkSize + 1) + x;
            unsigned short bottomRight = bottomLeft + 1;

            indices.push_back(topLeft);
            indices.push_back(bottomLeft);
            indices.push_back(topRight);

            indices.push_back(topRight);
            indices.push_back(bottomLeft);
            indices.push_back(bottomRight);
        }
    }

    // Add a skirt to hide seams
    int baseVertexIndex = vertices.size() / 3;
    float skirtDepth = 20.0f; // How far down the skirt goes

    // Bottom edge
    for (int x = 0; x <= lodChunkSize; x++) {
        int topIndex = x;
        Vector3 topVertex = { vertices[topIndex*3], vertices[topIndex*3+1], vertices[topIndex*3+2] };
        vertices.push_back(topVertex.x); vertices.push_back(topVertex.y - skirtDepth); vertices.push_back(topVertex.z);
        texcoords.push_back((float)(x * step) / chunkSize); texcoords.push_back(1.0f);
    }
    for (int x = 0; x < lodChunkSize; x++) {
        unsigned short tl = x;
        unsigned short tr = x + 1;
        unsigned short bl = baseVertexIndex + x;
        unsigned short br = baseVertexIndex + x + 1;
        indices.push_back(tl); indices.push_back(bl); indices.push_back(tr);
        indices.push_back(tr); indices.push_back(bl); indices.push_back(br);
    }
    baseVertexIndex += (lodChunkSize + 1);

    // Top edge
    for (int x = 0; x <= lodChunkSize; x++) {
        int topIndex = lodChunkSize * (lodChunkSize + 1) + x;
        Vector3 topVertex = { vertices[topIndex*3], vertices[topIndex*3+1], vertices[topIndex*3+2] };
        vertices.push_back(topVertex.x); vertices.push_back(topVertex.y - skirtDepth); vertices.push_back(topVertex.z);
        texcoords.push_back((float)(x * step) / chunkSize); texcoords.push_back(0.0f);
    }
    for (int x = 0; x < lodChunkSize; x++) {
        unsigned short tl = lodChunkSize * (lodChunkSize + 1) + x;
        unsigned short tr = tl + 1;
        unsigned short bl = baseVertexIndex + x;
        unsigned short br = baseVertexIndex + x + 1;
        indices.push_back(tr); indices.push_back(br); indices.push_back(tl);
        indices.push_back(tl); indices.push_back(br); indices.push_back(bl);
    }
    baseVertexIndex += (lodChunkSize + 1);

    // Left edge
    for (int z = 0; z <= lodChunkSize; z++) {
        int topIndex = z * (lodChunkSize + 1);
        Vector3 topVertex = { vertices[topIndex*3], vertices[topIndex*3+1], vertices[topIndex*3+2] };
        vertices.push_back(topVertex.x); vertices.push_back(topVertex.y - skirtDepth); vertices.push_back(topVertex.z);
        texcoords.push_back(0.0f); texcoords.push_back((float)(z * step) / chunkSize);
    }
    for (int z = 0; z < lodChunkSize; z++) {
        unsigned short tl = z * (lodChunkSize + 1);
        unsigned short bl = (z + 1) * (lodChunkSize + 1);
        unsigned short tr = baseVertexIndex + z;
        unsigned short br = baseVertexIndex + z + 1;
        indices.push_back(tr); indices.push_back(br); indices.push_back(tl);
        indices.push_back(tl); indices.push_back(br); indices.push_back(bl);
    }
    baseVertexIndex += (lodChunkSize + 1);

    // Right edge
    for (int z = 0; z <= lodChunkSize; z++) {
        int topIndex = z * (lodChunkSize + 1) + lodChunkSize;
        Vector3 topVertex = { vertices[topIndex*3], vertices[topIndex*3+1], vertices[topIndex*3+2] };
        vertices.push_back(topVertex.x); vertices.push_back(topVertex.y - skirtDepth); vertices.push_back(topVertex.z);
        texcoords.push_back(1.0f); texcoords.push_back((float)(z * step) / chunkSize);
    }
    for (int z = 0; z < lodChunkSize; z++) {
        unsigned short tl = z * (lodChunkSize + 1) + lodChunkSize;
        unsigned short bl = (z + 1) * (lodChunkSize + 1) + lodChunkSize;
        unsigned short tr = baseVertexIndex + z;
        unsigned short br = baseVertexIndex + z + 1;
        indices.push_back(tl); indices.push_back(bl); indices.push_back(tr);
        indices.push_back(tr); indices.push_back(bl); indices.push_back(br);
    }

    Mesh mesh = { 0 };
    mesh.vertexCount = vertices.size() / 3;
    mesh.triangleCount = indices.size() / 3;

    mesh.vertices = (float *)MemAlloc(vertices.size() * sizeof(float));
    memcpy(mesh.vertices, vertices.data(), vertices.size() * sizeof(float));

    mesh.texcoords = (float *)MemAlloc(texcoords.size() * sizeof(float));
    memcpy(mesh.texcoords, texcoords.data(), texcoords.size() * sizeof(float));

    mesh.indices = (unsigned short *)MemAlloc(indices.size() * sizeof(unsigned short));
    memcpy(mesh.indices, indices.data(), indices.size() * sizeof(unsigned short));

    // Calculate normals
    normals.resize(vertices.size(), 0.0f);
    for (size_t i = 0; i < indices.size(); i += 3) {
        unsigned short i1 = indices[i];
        unsigned short i2 = indices[i+1];
        unsigned short i3 = indices[i+2];

        Vector3 v1 = { vertices[i1*3], vertices[i1*3+1], vertices[i1*3+2] };
        Vector3 v2 = { vertices[i2*3], vertices[i2*3+1], vertices[i2*3+2] };
        Vector3 v3 = { vertices[i3*3], vertices[i3*3+1], vertices[i3*3+2] };

        Vector3 edge1 = { v2.x - v1.x, v2.y - v1.y, v2.z - v1.z };
        Vector3 edge2 = { v3.x - v1.x, v3.y - v1.y, v3.z - v1.z };

        Vector3 normal = Vector3CrossProduct(edge1, edge2);
        normal = Vector3Normalize(normal);

        normals[i1*3] += normal.x; normals[i1*3+1] += normal.y; normals[i1*3+2] += normal.z;
        normals[i2*3] += normal.x; normals[i2*3+1] += normal.y; normals[i2*3+2] += normal.z;
        normals[i3*3] += normal.x; normals[i3*3+1] += normal.y; normals[i3*3+2] += normal.z;
    }

    // Normalize all normals
    for (size_t i = 0; i < normals.size(); i+=3) {
        Vector3 normal = { normals[i], normals[i+1], normals[i+2] };
        normal = Vector3Normalize(normal);
        normals[i] = normal.x;
        normals[i+1] = normal.y;
        normals[i+2] = normal.z;
    }

    mesh.normals = (float *)MemAlloc(normals.size() * sizeof(float));
    memcpy(mesh.normals, normals.data(), normals.size() * sizeof(float));

    UploadMesh(&mesh, true);

    return mesh;
}

int main() {
    const int screenWidth = 1200;
    const int screenHeight = 800;

    InitWindow(screenWidth, screenHeight, "Minecraft-like World with Enhanced Sky");

    Camera3D camera = { 0 };
    camera.position = (Vector3){ 50.0f, 50.0f, 50.0f };
    camera.target = (Vector3){ 0.0f, 0.0f, 0.0f };
    camera.up = (Vector3){ 0.0f, 1.0f, 0.0f };
    camera.fovy = 45.0f;
    camera.projection = CAMERA_PERSPECTIVE;

    // Initialize sky system
    float timeOfDay = 0.25f; // Start at dawn
    float gameTime = 0.0f;

    Cloud clouds[MAX_CLOUDS];
    Star stars[MAX_STARS];

    InitializeClouds(clouds);
    InitializeStars(stars);

    Shader shader = LoadShader("lighting.vs", "lighting.fs");
    Image checked = GenImageChecked(2, 2, 1, 1, DARKGRAY, WHITE);
    Texture2D texture = LoadTextureFromImage(checked);
    UnloadImage(checked);

    std::vector<TerrainChunk> terrain(TERRAIN_GRID_WIDTH * TERRAIN_GRID_DEPTH);

    for (int z = 0; z < TERRAIN_GRID_DEPTH; z++) {
        for (int x = 0; x < TERRAIN_GRID_WIDTH; x++) {
            int index = z * TERRAIN_GRID_WIDTH + x;
            terrain[index].position = {
                (x - TERRAIN_GRID_WIDTH / 2.0f) * TERRAIN_CHUNK_SIZE,
                0,
                (z - TERRAIN_GRID_DEPTH / 2.0f) * TERRAIN_CHUNK_SIZE
            };

            for (int lod = 0; lod < NUM_LODS; lod++) {
                Mesh mesh = GenTerrainMesh(TERRAIN_CHUNK_SIZE, lod, terrain[index].position);
                terrain[index].lods[lod] = LoadModelFromMesh(mesh);
                terrain[index].lods[lod].materials[0].shader = shader;
                terrain[index].lods[lod].materials[0].maps[MATERIAL_MAP_DIFFUSE].texture = texture;
            }
        }
    }

    Vector3 lightDir = Vector3Normalize((Vector3){ -1.0f, -1.0f, -1.0f });
    float lightColor[] = { 1.0f, 1.0f, 1.0f, 1.0f };

    int lightDirLoc = GetShaderLocation(shader, "lightDir");
    int lightColorLoc = GetShaderLocation(shader, "lightColor");
    int viewPosLoc = GetShaderLocation(shader, "viewPos");

    SetShaderValue(shader, lightDirLoc, &lightDir, SHADER_UNIFORM_VEC3);
    SetShaderValue(shader, lightColorLoc, lightColor, SHADER_UNIFORM_VEC4);

    // Load skybox (PNG fallback)
    Shader shdrSkybox = LoadShader("skybox.vs", "skybox.fs");
    Model skybox = { 0 };
    bool skyboxLoaded = false;

    Image panoramaImg = LoadImage("skybox.png");
    if (panoramaImg.data != NULL) {
        skyboxLoaded = true;
        TextureCubemap cubemap = LoadTextureCubemap(panoramaImg, CUBEMAP_LAYOUT_AUTO_DETECT);
        UnloadImage(panoramaImg);

        skybox = LoadModelFromMesh(GenMeshCube(50.0f, 50.0f, 50.0f));
        skybox.materials[0].shader = shdrSkybox;
        // Bind cubemap texture to samplerCube uniform explicitly
        int cubeLoc = GetShaderLocation(shdrSkybox, "skybox");
        SetShaderValueTexture(shdrSkybox, cubeLoc, cubemap);
        skybox.materials[0].maps[MATERIAL_MAP_CUBEMAP].texture = cubemap;
    } else {
        TraceLog(LOG_WARNING, "Failed to load skybox texture: skybox.png");
    }

    SetTargetFPS(60);

    while (!WindowShouldClose()) {
        float deltaTime = GetFrameTime();
        gameTime += deltaTime;

        // Update day/night cycle
        timeOfDay += deltaTime / DAY_CYCLE_DURATION;
        if (timeOfDay > 1.0f) timeOfDay -= 1.0f;

        // Update sky elements
        UpdateClouds(clouds, deltaTime);

        UpdateCamera(&camera, CAMERA_FIRST_PERSON);

        float cameraPos[] = { camera.position.x, camera.position.y, camera.position.z };
        SetShaderValue(shader, viewPosLoc, cameraPos, SHADER_UNIFORM_VEC3);

        // Update lighting based on time of day
        Vector3 currentSunPos = GetSunPosition(timeOfDay);
        Vector3 lightDirection = Vector3Normalize(Vector3Subtract((Vector3){0,0,0}, currentSunPos));
        float lightIntensity = std::max(0.2f, currentSunPos.y / 1000.0f); // Minimum ambient light

        float lightColor[] = { lightIntensity, lightIntensity * 0.9f, lightIntensity * 0.8f, 1.0f };
        SetShaderValue(shader, lightDirLoc, &lightDirection, SHADER_UNIFORM_VEC3);
        SetShaderValue(shader, lightColorLoc, lightColor, SHADER_UNIFORM_VEC4);

        // Get current sky colors
        SkyColors skyColors = GetSkyColors(timeOfDay);

        BeginDrawing();
            ClearBackground(skyColors.horizon);
            // Update skybox shader matrices
            if (skyboxLoaded) {
                Matrix camView = GetCameraMatrix(camera); // view matrix
                camView.m12 = camView.m13 = camView.m14 = 0.0f; // remove translation
                Matrix camProj = MatrixPerspective(camera.fovy*DEG2RAD, (float)GetScreenWidth()/GetScreenHeight(), 0.01f, 1000.0f);
                SetShaderValueMatrix(shdrSkybox, GetShaderLocation(shdrSkybox, "view"), camView);
                SetShaderValueMatrix(shdrSkybox, GetShaderLocation(shdrSkybox, "projection"), camProj);
            }
            BeginMode3D(camera);
                // Draw enhanced sky system
                rlDisableDepthTest();

                // Draw sky gradient
                DrawSkyGradient(camera, skyColors);

                // Draw celestial objects
                Vector3 sunPos = GetSunPosition(timeOfDay);
                Vector3 moonPos = GetMoonPosition(timeOfDay);

                DrawSun(sunPos, timeOfDay);
                DrawMoon(moonPos, timeOfDay);
                DrawStars(stars, timeOfDay, gameTime);

                // Draw clouds
                BeginBlendMode(BLEND_ALPHA);
                DrawClouds(clouds, camera, timeOfDay);
                EndBlendMode();

                rlEnableDepthTest();

                // Draw original skybox if available (as fallback)
                if (skyboxLoaded) {
                    rlDisableBackfaceCulling();
                    rlDisableDepthMask();
                    // Make skybox more transparent to show our custom sky
                    DrawModel(skybox, camera.position, 1.0f, (Color){255, 255, 255, 100});
                    rlEnableDepthMask();
                    rlEnableBackfaceCulling();
                }

                // Draw terrain with fog effect
                for (const auto& chunk : terrain) {
                    float distance = Vector3Distance(camera.position, chunk.position);
                    int lod = 0;
                    if (distance > 200) lod = 2;
                    else if (distance > 100) lod = 1;

                    // Apply fog based on distance and time of day
                    Color tintColor = WHITE;
                    if (distance > 150.0f) {
                        float fogFactor = std::min(1.0f, (distance - 150.0f) / 200.0f);
                        tintColor = (Color){
                            (unsigned char)(255 * (1.0f - fogFactor) + skyColors.fog.r * fogFactor),
                            (unsigned char)(255 * (1.0f - fogFactor) + skyColors.fog.g * fogFactor),
                            (unsigned char)(255 * (1.0f - fogFactor) + skyColors.fog.b * fogFactor),
                            255
                        };
                    }

                    DrawModel(chunk.lods[lod], chunk.position, 1.0f, tintColor);
                }
            EndMode3D();

            // Draw UI
            DrawFPS(10, 10);

            // Draw time and weather info
            const char* timeText;
            float t = fmod(timeOfDay, 1.0f);
            if (t < 0.2f) timeText = "Night";
            else if (t < 0.3f) timeText = "Dawn";
            else if (t < 0.7f) timeText = "Day";
            else if (t < 0.8f) timeText = "Dusk";
            else timeText = "Night";

            DrawText(TextFormat("Time: %s (%.2f)", timeText, timeOfDay), 10, 40, 20, WHITE);
            DrawText("Enhanced Minecraft-like Sky System", 10, screenHeight - 60, 20, WHITE);
            DrawText("Features: Dynamic sky, sun/moon, clouds, stars, fog", 10, screenHeight - 40, 16, WHITE);
            DrawText("WASD: Move, Mouse: Look around", 10, screenHeight - 20, 16, WHITE);
        EndDrawing();
    }

    UnloadShader(shader);
    UnloadTexture(texture);

    if (skyboxLoaded) {
        UnloadTexture(skybox.materials[0].maps[MATERIAL_MAP_CUBEMAP].texture);
        UnloadModel(skybox);
    }
    UnloadShader(shdrSkybox);
    for (const auto& chunk : terrain) {
        for (int lod = 0; lod < NUM_LODS; lod++) {
            UnloadModel(chunk.lods[lod]);
        }
    }
    CloseWindow();

    return 0;
}

